from pydantic import BaseModel, HttpUrl, validator, Field
from typing import Optional, List, Dict, Any
import re
from urllib.parse import urlparse

class ScrapeRequest(BaseModel):
    """Request model for scraping endpoint"""
    url: HttpUrl = Field(..., description="The URL to scrape")
    wait_for_js: bool = Field(True, description="Whether to wait for JavaScript to render")
    timeout: int = Field(8, ge=1, le=9, description="Request timeout in seconds (max 9 for Vercel)")
    extract_links: bool = Field(False, description="Whether to extract all links from the page")
    extract_images: bool = Field(False, description="Whether to extract all images from the page")
    
    @validator('url')
    def validate_url(cls, v):
        """Validate URL format and scheme"""
        url_str = str(v)
        parsed = urlparse(url_str)
        
        # Check if scheme is http or https
        if parsed.scheme not in ['http', 'https']:
            raise ValueError('URL must use http or https scheme')
        
        # Check if hostname exists
        if not parsed.netloc:
            raise ValueError('URL must have a valid hostname')
        
        # Basic security check - block localhost and private IPs
        hostname = parsed.hostname
        if hostname:
            # Block localhost
            if hostname.lower() in ['localhost', '127.0.0.1', '::1']:
                raise ValueError('Localhost URLs are not allowed')
            
            # Block private IP ranges (basic check)
            if re.match(r'^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)', hostname):
                raise ValueError('Private IP addresses are not allowed')
        
        return v

class ScrapeResponse(BaseModel):
    """Response model for successful scraping"""
    success: bool = True
    url: str
    title: str
    content: str
    meta_description: Optional[str] = None
    links: List[str] = []
    images: List[str] = []
    processing_time: float
    timestamp: float

class ErrorResponse(BaseModel):
    """Response model for errors"""
    success: bool = False
    error: str
    status_code: int
    timestamp: float

class ScrapingResult(BaseModel):
    """Internal model for scraping results"""
    title: str = ""
    content: str = ""
    meta_description: str = ""
    links: List[str] = []
    images: List[str] = []
    status_code: int = 200
    final_url: str = ""
