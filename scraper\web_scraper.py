import asyncio
import logging
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin, urlparse
import re
import time

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

from .models import ScrapingResult

logger = logging.getLogger(__name__)

class WebScraper:
    """Web scraper with JavaScript rendering support using Playwright"""
    
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.context = None
        self._ready = False
        
    async def initialize(self):
        """Initialize Playwright browser"""
        if not PLAYWRIGHT_AVAILABLE:
            logger.warning("Playwright not available, falling back to basic scraping")
            self._ready = True
            return
            
        try:
            self.playwright = await async_playwright().start()
            
            # Use Chromium for better compatibility and performance
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            )
            
            # Create a persistent context for better performance
            self.context = await self.browser.new_context(
                viewport={'width': 1280, 'height': 720},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            )
            
            self._ready = True
            logger.info("Playwright browser initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Playwright: {e}")
            self._ready = False
            
    async def cleanup(self):
        """Cleanup browser resources"""
        try:
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            
    def is_ready(self) -> bool:
        """Check if scraper is ready"""
        return self._ready
        
    async def scrape(
        self,
        url: str,
        wait_for_js: bool = True,
        timeout: int = 8,
        extract_links: bool = False,
        extract_images: bool = False
    ) -> Dict[str, Any]:
        """
        Scrape content from a URL
        
        Args:
            url: The URL to scrape
            wait_for_js: Whether to wait for JavaScript rendering
            timeout: Request timeout in seconds
            extract_links: Whether to extract links
            extract_images: Whether to extract images
            
        Returns:
            Dictionary containing scraped content
        """
        if not self._ready:
            raise Exception("Scraper not initialized")
            
        if PLAYWRIGHT_AVAILABLE and wait_for_js:
            return await self._scrape_with_playwright(
                url, timeout, extract_links, extract_images
            )
        else:
            return await self._scrape_basic(url, timeout, extract_links, extract_images)
            
    async def _scrape_with_playwright(
        self,
        url: str,
        timeout: int,
        extract_links: bool,
        extract_images: bool
    ) -> Dict[str, Any]:
        """Scrape using Playwright for JavaScript rendering"""
        page = None
        try:
            page = await self.context.new_page()
            
            # Set timeout
            page.set_default_timeout(timeout * 1000)
            
            # Navigate to the page
            response = await page.goto(url, wait_until='domcontentloaded')
            
            if not response:
                raise Exception("Failed to load page")
                
            # Wait a bit for JavaScript to execute
            await asyncio.sleep(1)
            
            # Extract content
            result = await self._extract_content_playwright(
                page, url, extract_links, extract_images
            )
            
            result['status_code'] = response.status
            result['final_url'] = page.url
            
            return result
            
        except asyncio.TimeoutError:
            raise asyncio.TimeoutError("Page load timeout")
        except Exception as e:
            logger.error(f"Playwright scraping error: {e}")
            raise Exception(f"Scraping failed: {str(e)}")
        finally:
            if page:
                await page.close()
                
    async def _extract_content_playwright(
        self,
        page: Page,
        base_url: str,
        extract_links: bool,
        extract_images: bool
    ) -> Dict[str, Any]:
        """Extract content using Playwright"""
        result = {
            'title': '',
            'content': '',
            'meta_description': '',
            'links': [],
            'images': []
        }
        
        try:
            # Extract title
            title_element = await page.query_selector('title')
            if title_element:
                result['title'] = await title_element.inner_text()
                
            # Extract meta description
            meta_desc = await page.query_selector('meta[name="description"]')
            if meta_desc:
                result['meta_description'] = await meta_desc.get_attribute('content') or ''
                
            # Extract main content (remove script and style tags)
            content = await page.evaluate('''() => {
                // Remove script and style elements
                const scripts = document.querySelectorAll('script, style, noscript');
                scripts.forEach(el => el.remove());
                
                // Get text content from body
                const body = document.body;
                return body ? body.innerText : '';
            }''')
            
            result['content'] = self._clean_text(content)
            
            # Extract links if requested
            if extract_links:
                links = await page.evaluate('''() => {
                    const links = Array.from(document.querySelectorAll('a[href]'));
                    return links.map(link => link.href).filter(href => href);
                }''')
                result['links'] = list(set(links))  # Remove duplicates
                
            # Extract images if requested
            if extract_images:
                images = await page.evaluate('''() => {
                    const images = Array.from(document.querySelectorAll('img[src]'));
                    return images.map(img => img.src).filter(src => src);
                }''')
                result['images'] = list(set(images))  # Remove duplicates
                
        except Exception as e:
            logger.error(f"Content extraction error: {e}")
            
        return result
        
    async def _scrape_basic(
        self,
        url: str,
        timeout: int,
        extract_links: bool,
        extract_images: bool
    ) -> Dict[str, Any]:
        """Basic scraping without JavaScript rendering (fallback)"""
        import aiohttp
        from bs4 import BeautifulSoup
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        timeout_config = aiohttp.ClientTimeout(total=timeout)
        
        async with aiohttp.ClientSession(timeout=timeout_config, headers=headers) as session:
            async with session.get(url) as response:
                if response.status >= 400:
                    raise Exception(f"HTTP {response.status}: {response.reason}")
                    
                html = await response.text()
                soup = BeautifulSoup(html, 'html.parser')
                
                # Remove script and style elements
                for script in soup(["script", "style", "noscript"]):
                    script.decompose()
                    
                result = {
                    'title': '',
                    'content': '',
                    'meta_description': '',
                    'links': [],
                    'images': [],
                    'status_code': response.status,
                    'final_url': str(response.url)
                }
                
                # Extract title
                title_tag = soup.find('title')
                if title_tag:
                    result['title'] = title_tag.get_text().strip()
                    
                # Extract meta description
                meta_desc = soup.find('meta', attrs={'name': 'description'})
                if meta_desc:
                    result['meta_description'] = meta_desc.get('content', '').strip()
                    
                # Extract content
                body = soup.find('body')
                if body:
                    result['content'] = self._clean_text(body.get_text())
                    
                # Extract links if requested
                if extract_links:
                    links = [urljoin(url, a.get('href')) for a in soup.find_all('a', href=True)]
                    result['links'] = list(set(filter(None, links)))
                    
                # Extract images if requested
                if extract_images:
                    images = [urljoin(url, img.get('src')) for img in soup.find_all('img', src=True)]
                    result['images'] = list(set(filter(None, images)))
                    
                return result
                
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content"""
        if not text:
            return ""
            
        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove very long lines (likely not useful content)
        lines = text.split('\n')
        cleaned_lines = [line.strip() for line in lines if len(line.strip()) < 1000]
        
        return '\n'.join(cleaned_lines).strip()
