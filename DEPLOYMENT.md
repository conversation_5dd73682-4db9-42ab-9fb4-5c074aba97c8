# Vercel Deployment Guide

## Fixing the 404 Error

The 404 error you're experiencing is likely due to one of these common issues:

### 1. **Project Structure Issue**
Make sure your project structure looks like this:
```
scraper/
├── api/
│   └── index.py          # Vercel entry point
├── scraper/
│   ├── __init__.py
│   ├── models.py
│   ├── web_scraper.py
│   └── validators.py
├── main.py               # Main FastAPI app
├── requirements.txt      # Dependencies
└── vercel.json          # Vercel configuration
```

### 2. **Vercel Configuration**
Your `vercel.json` should be configured correctly (which it now is).

### 3. **Deployment Steps**

#### Option A: Deploy with <PERSON>wright (Full Features)
```bash
# Use the full requirements.txt
cp requirements.txt requirements-backup.txt

# Deploy
vercel --prod
```

#### Option B: Deploy without Playwright (Basic Scraping Only)
If Playwright causes issues, use the lightweight version:
```bash
# Use lightweight requirements
cp requirements-vercel.txt requirements.txt

# Deploy
vercel --prod

# Restore original requirements for local development
cp requirements-backup.txt requirements.txt
```

### 4. **Testing the Deployment**

Once deployed, test these endpoints:

1. **Health Check:**
   ```bash
   curl https://your-app.vercel.app/health
   ```

2. **API Documentation:**
   Visit: `https://your-app.vercel.app/docs`

3. **Basic Scraping:**
   ```bash
   curl -X POST "https://your-app.vercel.app/scrape" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://example.com", "wait_for_js": false}'
   ```

### 5. **Common Issues and Solutions**

#### Issue: 404 Not Found
**Solution:** 
- Ensure `api/index.py` exists and is properly configured
- Check that `vercel.json` routes are correct
- Redeploy with `vercel --prod`

#### Issue: Module Import Errors
**Solution:**
- Check that all dependencies are in `requirements.txt`
- Ensure Python path is set correctly in `api/index.py`

#### Issue: Playwright Installation Fails
**Solution:**
- Use `requirements-vercel.txt` (without Playwright)
- The app will fall back to basic scraping automatically

#### Issue: Function Timeout
**Solution:**
- Reduce timeout values in requests
- Use `wait_for_js: false` for faster responses

### 6. **Environment Variables**

If needed, you can set environment variables in Vercel:
```bash
vercel env add PYTHONPATH .
vercel env add LOG_LEVEL INFO
```

### 7. **Debugging**

To debug deployment issues:

1. **Check Vercel Function Logs:**
   ```bash
   vercel logs
   ```

2. **Test Locally:**
   ```bash
   python main.py
   # Test at http://localhost:8000
   ```

3. **Validate Configuration:**
   ```bash
   vercel dev
   # Test locally with Vercel environment
   ```

### 8. **Performance Optimization**

For better performance on Vercel:

1. **Use Basic Scraping for Simple Sites:**
   ```json
   {"url": "https://example.com", "wait_for_js": false}
   ```

2. **Set Appropriate Timeouts:**
   ```json
   {"url": "https://example.com", "timeout": 5}
   ```

3. **Limit Extracted Content:**
   ```json
   {
     "url": "https://example.com",
     "extract_links": false,
     "extract_images": false
   }
   ```

### 9. **Alternative: Deploy to Other Platforms**

If Vercel continues to have issues, consider these alternatives:

- **Railway:** Better for apps with heavy dependencies
- **Render:** Good for Python apps with longer timeouts
- **Google Cloud Run:** More flexible for complex applications
- **AWS Lambda:** With custom layers for Playwright

### 10. **Next Steps**

1. Try redeploying with the fixed configuration
2. Test the health endpoint first
3. If Playwright doesn't work, use the basic scraping version
4. Monitor the Vercel function logs for any errors

The application is now configured to gracefully handle both scenarios (with and without Playwright), so it should work even if browser automation isn't available in the serverless environment.
