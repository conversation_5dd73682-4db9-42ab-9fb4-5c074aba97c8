"""
Example usage of the Web Scraping API
"""
import requests
import json
import time

# Configuration
API_URL = "https://your-app.vercel.app"  # Replace with your deployed URL
# For local testing: API_URL = "http://localhost:8000"

def scrape_website(url, **options):
    """
    Scrape a website using the API
    
    Args:
        url (str): The URL to scrape
        **options: Additional options (wait_for_js, timeout, extract_links, extract_images)
    
    Returns:
        dict: Scraped content and metadata
    """
    payload = {
        "url": url,
        "wait_for_js": True,
        "timeout": 8,
        "extract_links": False,
        "extract_images": False,
        **options
    }
    
    try:
        response = requests.post(
            f"{API_URL}/scrape",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=15  # Client timeout
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error {response.status_code}: {response.json()}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None

def example_basic_scraping():
    """Example: Basic website scraping"""
    print("=== Basic Website Scraping ===")
    
    url = "https://example.com"
    result = scrape_website(url)
    
    if result:
        print(f"URL: {result['url']}")
        print(f"Title: {result['title']}")
        print(f"Meta Description: {result['meta_description']}")
        print(f"Content Preview: {result['content'][:200]}...")
        print(f"Processing Time: {result['processing_time']:.2f}s")
    
    print()

def example_with_links_and_images():
    """Example: Scraping with link and image extraction"""
    print("=== Scraping with Links and Images ===")
    
    url = "https://httpbin.org/html"
    result = scrape_website(
        url,
        extract_links=True,
        extract_images=True
    )
    
    if result:
        print(f"URL: {result['url']}")
        print(f"Title: {result['title']}")
        print(f"Links found: {len(result['links'])}")
        print(f"Images found: {len(result['images'])}")
        
        if result['links']:
            print("Sample links:")
            for link in result['links'][:5]:  # Show first 5 links
                print(f"  - {link}")
        
        if result['images']:
            print("Sample images:")
            for image in result['images'][:3]:  # Show first 3 images
                print(f"  - {image}")
    
    print()

def example_javascript_heavy_site():
    """Example: Scraping a JavaScript-heavy site"""
    print("=== JavaScript-Heavy Site ===")
    
    # This site loads content via JavaScript
    url = "https://httpbin.org/json"
    
    # First try without JavaScript rendering
    print("Without JavaScript rendering:")
    result_no_js = scrape_website(url, wait_for_js=False)
    if result_no_js:
        print(f"Content length: {len(result_no_js['content'])}")
    
    # Then try with JavaScript rendering
    print("With JavaScript rendering:")
    result_with_js = scrape_website(url, wait_for_js=True)
    if result_with_js:
        print(f"Content length: {len(result_with_js['content'])}")
    
    print()

def example_error_handling():
    """Example: Error handling"""
    print("=== Error Handling Examples ===")
    
    # Invalid URL
    print("Testing invalid URL:")
    result = scrape_website("not-a-valid-url")
    # Should return None and print error
    
    # Timeout test
    print("Testing timeout:")
    result = scrape_website("https://httpbin.org/delay/10", timeout=3)
    # Should timeout and return error
    
    # Private IP (should be blocked)
    print("Testing blocked URL:")
    result = scrape_website("http://127.0.0.1:8080")
    # Should be blocked by security validation
    
    print()

def example_batch_scraping():
    """Example: Batch scraping multiple URLs"""
    print("=== Batch Scraping ===")
    
    urls = [
        "https://example.com",
        "https://httpbin.org/html",
        "https://httpbin.org/json"
    ]
    
    results = []
    
    for i, url in enumerate(urls, 1):
        print(f"Scraping {i}/{len(urls)}: {url}")
        
        result = scrape_website(url)
        if result:
            results.append({
                'url': url,
                'title': result['title'],
                'content_length': len(result['content']),
                'processing_time': result['processing_time']
            })
        
        # Be respectful - add delay between requests
        time.sleep(1)
    
    print("\nBatch Results Summary:")
    for result in results:
        print(f"  {result['url']}")
        print(f"    Title: {result['title']}")
        print(f"    Content Length: {result['content_length']}")
        print(f"    Processing Time: {result['processing_time']:.2f}s")
    
    print()

def check_api_health():
    """Check if the API is healthy"""
    print("=== API Health Check ===")
    
    try:
        response = requests.get(f"{API_URL}/health", timeout=10)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"API Status: {health_data['status']}")
            print(f"Scraper Ready: {health_data['scraper_ready']}")
            return True
        else:
            print(f"Health check failed: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"Health check failed: {e}")
        return False

def main():
    """Run all examples"""
    print("🕷️  Web Scraping API Examples\n")
    
    # Check API health first
    if not check_api_health():
        print("❌ API is not healthy. Please check your deployment.")
        return
    
    print("✅ API is healthy. Running examples...\n")
    
    # Run examples
    example_basic_scraping()
    example_with_links_and_images()
    example_javascript_heavy_site()
    example_error_handling()
    example_batch_scraping()
    
    print("🎉 All examples completed!")

if __name__ == "__main__":
    main()
