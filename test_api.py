"""
Test script for the Web Scraping API
"""
import asyncio
import aiohttp
import json
import time

# Configuration
BASE_URL = "http://localhost:8000"  # Change to your deployed URL
TEST_URLS = [
    "https://httpbin.org/html",
    "https://example.com",
    "https://httpbin.org/json",
]

async def test_health_endpoint():
    """Test the health check endpoint"""
    print("Testing health endpoint...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/health") as response:
            data = await response.json()
            print(f"Health check: {data}")
            assert response.status == 200
            assert data["status"] == "healthy"
    
    print("✅ Health endpoint test passed\n")

async def test_scrape_endpoint(url: str, **kwargs):
    """Test the scrape endpoint with a given URL"""
    print(f"Testing scrape endpoint with URL: {url}")
    
    payload = {
        "url": url,
        "wait_for_js": True,
        "timeout": 8,
        "extract_links": False,
        "extract_images": False,
        **kwargs
    }
    
    start_time = time.time()
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{BASE_URL}/scrape",
            json=payload,
            headers={"Content-Type": "application/json"}
        ) as response:
            
            response_time = time.time() - start_time
            
            if response.status == 200:
                data = await response.json()
                print(f"✅ Success! Response time: {response_time:.2f}s")
                print(f"   Title: {data.get('title', 'N/A')[:100]}...")
                print(f"   Content length: {len(data.get('content', ''))}")
                print(f"   Processing time: {data.get('processing_time', 0):.2f}s")
                if data.get('links'):
                    print(f"   Links found: {len(data['links'])}")
                if data.get('images'):
                    print(f"   Images found: {len(data['images'])}")
            else:
                error_data = await response.json()
                print(f"❌ Error {response.status}: {error_data}")
    
    print()

async def test_invalid_url():
    """Test with invalid URL"""
    print("Testing invalid URL...")
    
    payload = {
        "url": "not-a-valid-url",
        "timeout": 5
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{BASE_URL}/scrape",
            json=payload,
            headers={"Content-Type": "application/json"}
        ) as response:
            
            if response.status == 422:  # Validation error
                print("✅ Invalid URL correctly rejected")
            else:
                data = await response.json()
                print(f"❌ Unexpected response: {response.status} - {data}")
    
    print()

async def test_timeout():
    """Test timeout handling"""
    print("Testing timeout handling...")
    
    payload = {
        "url": "https://httpbin.org/delay/10",  # This will timeout
        "timeout": 3
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{BASE_URL}/scrape",
            json=payload,
            headers={"Content-Type": "application/json"}
        ) as response:
            
            if response.status == 408:  # Timeout
                print("✅ Timeout correctly handled")
            else:
                data = await response.json()
                print(f"Response: {response.status} - {data}")
    
    print()

async def test_rate_limiting():
    """Test rate limiting (make multiple requests quickly)"""
    print("Testing rate limiting...")
    
    payload = {
        "url": "https://httpbin.org/json",
        "timeout": 5
    }
    
    success_count = 0
    rate_limited_count = 0
    
    # Make 25 requests quickly (should hit rate limit)
    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(25):
            task = session.post(
                f"{BASE_URL}/scrape",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        for response in responses:
            if isinstance(response, Exception):
                continue
            
            if response.status == 200:
                success_count += 1
            elif response.status == 429:
                rate_limited_count += 1
            
            response.close()
    
    print(f"   Successful requests: {success_count}")
    print(f"   Rate limited requests: {rate_limited_count}")
    
    if rate_limited_count > 0:
        print("✅ Rate limiting is working")
    else:
        print("⚠️  Rate limiting may not be working as expected")
    
    print()

async def main():
    """Run all tests"""
    print("🧪 Starting Web Scraping API Tests\n")
    
    try:
        # Test health endpoint
        await test_health_endpoint()
        
        # Test scraping with different URLs
        for url in TEST_URLS:
            await test_scrape_endpoint(url)
        
        # Test with link extraction
        await test_scrape_endpoint(
            "https://example.com",
            extract_links=True,
            extract_images=True
        )
        
        # Test error cases
        await test_invalid_url()
        await test_timeout()
        
        # Test rate limiting
        await test_rate_limiting()
        
        print("🎉 All tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
