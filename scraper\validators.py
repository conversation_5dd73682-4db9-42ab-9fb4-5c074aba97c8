import re
import socket
from urllib.parse import urlparse
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)

class URLValidator:
    """URL validation and security checks"""
    
    # Blocked domains (can be extended)
    BLOCKED_DOMAINS = {
        'localhost',
        '127.0.0.1',
        '::1',
        '0.0.0.0'
    }
    
    # Private IP ranges
    PRIVATE_IP_PATTERNS = [
        r'^10\.',
        r'^172\.(1[6-9]|2[0-9]|3[01])\.',
        r'^192\.168\.',
        r'^169\.254\.',  # Link-local
        r'^fc00:',       # IPv6 private
        r'^fe80:',       # IPv6 link-local
    ]
    
    # Suspicious patterns
    SUSPICIOUS_PATTERNS = [
        r'\.onion$',     # Tor hidden services
        r'localhost',
        r'127\.0\.0\.1',
        r'0\.0\.0\.0',
    ]
    
    @classmethod
    def validate_url(cls, url: str) -> tuple[bool, Optional[str]]:
        """
        Validate URL for security and accessibility
        
        Returns:
            (is_valid, error_message)
        """
        try:
            parsed = urlparse(url)
            
            # Check scheme
            if parsed.scheme not in ['http', 'https']:
                return False, "Only HTTP and HTTPS URLs are allowed"
            
            # Check if hostname exists
            if not parsed.netloc:
                return False, "URL must have a valid hostname"
            
            hostname = parsed.hostname
            if not hostname:
                return False, "Invalid hostname"
            
            # Check blocked domains
            if hostname.lower() in cls.BLOCKED_DOMAINS:
                return False, f"Access to {hostname} is not allowed"
            
            # Check for private IP addresses
            if cls._is_private_ip(hostname):
                return False, "Private IP addresses are not allowed"
            
            # Check for suspicious patterns
            if cls._has_suspicious_patterns(hostname):
                return False, "URL contains suspicious patterns"
            
            # Check URL length
            if len(url) > 2048:
                return False, "URL is too long"
            
            # Try to resolve hostname (basic check)
            try:
                socket.gethostbyname(hostname)
            except socket.gaierror:
                return False, f"Cannot resolve hostname: {hostname}"
            
            return True, None
            
        except Exception as e:
            return False, f"Invalid URL format: {str(e)}"
    
    @classmethod
    def _is_private_ip(cls, hostname: str) -> bool:
        """Check if hostname is a private IP address"""
        for pattern in cls.PRIVATE_IP_PATTERNS:
            if re.match(pattern, hostname, re.IGNORECASE):
                return True
        return False
    
    @classmethod
    def _has_suspicious_patterns(cls, hostname: str) -> bool:
        """Check for suspicious patterns in hostname"""
        for pattern in cls.SUSPICIOUS_PATTERNS:
            if re.search(pattern, hostname, re.IGNORECASE):
                return True
        return False

class ContentValidator:
    """Content validation and filtering"""
    
    @staticmethod
    def is_content_safe(content: str) -> bool:
        """Basic content safety check"""
        if not content:
            return True
            
        # Check content length
        if len(content) > 1_000_000:  # 1MB limit
            return False
            
        return True
    
    @staticmethod
    def sanitize_content(content: str) -> str:
        """Sanitize content for safe output"""
        if not content:
            return ""
        
        # Remove null bytes and control characters
        content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', content)
        
        # Limit content length
        if len(content) > 500_000:  # 500KB limit for response
            content = content[:500_000] + "... [Content truncated]"
        
        return content

class RateLimiter:
    """Simple in-memory rate limiter"""
    
    def __init__(self, max_requests: int = 10, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}
    
    def is_allowed(self, identifier: str) -> bool:
        """Check if request is allowed for given identifier"""
        import time
        
        current_time = time.time()
        
        # Clean old entries
        self.requests = {
            k: v for k, v in self.requests.items()
            if current_time - v['first_request'] < self.window_seconds
        }
        
        if identifier not in self.requests:
            self.requests[identifier] = {
                'count': 1,
                'first_request': current_time
            }
            return True
        
        request_data = self.requests[identifier]
        
        if current_time - request_data['first_request'] >= self.window_seconds:
            # Reset window
            self.requests[identifier] = {
                'count': 1,
                'first_request': current_time
            }
            return True
        
        if request_data['count'] >= self.max_requests:
            return False
        
        request_data['count'] += 1
        return True
