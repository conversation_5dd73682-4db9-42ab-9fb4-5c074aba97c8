from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, HttpUrl, validator
from typing import Optional, Dict, Any
import asyncio
import logging
import time
from urllib.parse import urlparse
import re
import os

# Import scraping modules
from scraper.web_scraper import WebScraper
from scraper.models import ScrapeRequest, ScrapeResponse, ErrorResponse
from scraper.validators import URLValidator, ContentValidator, RateLimiter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Web Scraping API",
    description="A FastAPI application for scraping web content with JavaScript rendering support",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize web scraper and rate limiter
scraper = WebScraper()
rate_limiter = RateLimiter(max_requests=20, window_seconds=60)  # 20 requests per minute

@app.on_event("startup")
async def startup_event():
    """Initialize resources on startup"""
    logger.info("Starting Web Scraping API...")
    await scraper.initialize()

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup resources on shutdown"""
    logger.info("Shutting down Web Scraping API...")
    await scraper.cleanup()

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Web Scraping API",
        "version": "1.0.0",
        "docs": "/docs",
        "endpoints": {
            "scrape": "/scrape (POST)",
            "health": "/health (GET)"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "scraper_ready": scraper.is_ready()
    }

@app.post("/scrape", response_model=ScrapeResponse)
async def scrape_url(request: ScrapeRequest, client_request: Request):
    """
    Scrape content from a given URL

    - **url**: The URL to scrape (required)
    - **wait_for_js**: Whether to wait for JavaScript to render (default: True)
    - **timeout**: Request timeout in seconds (default: 8, max: 9 for Vercel)
    - **extract_links**: Whether to extract all links from the page (default: False)
    - **extract_images**: Whether to extract all images from the page (default: False)
    """
    start_time = time.time()

    # Get client IP for rate limiting
    client_ip = client_request.client.host if client_request.client else "unknown"

    try:
        # Rate limiting check
        if not rate_limiter.is_allowed(client_ip):
            raise HTTPException(
                status_code=429,
                detail="Rate limit exceeded. Please try again later."
            )

        # Validate timeout for Vercel limits (leave 1 second buffer)
        if request.timeout > 8:
            raise HTTPException(
                status_code=400,
                detail="Timeout cannot exceed 8 seconds due to serverless limitations"
            )

        # Additional URL validation
        is_valid, error_msg = URLValidator.validate_url(str(request.url))
        if not is_valid:
            raise HTTPException(status_code=400, detail=error_msg)

        logger.info(f"Starting scrape for URL: {request.url} from IP: {client_ip}")

        # Set a hard timeout for the entire operation (8 seconds max)
        try:
            result = await asyncio.wait_for(
                scraper.scrape(
                    url=str(request.url),
                    wait_for_js=request.wait_for_js,
                    timeout=request.timeout,
                    extract_links=request.extract_links,
                    extract_images=request.extract_images
                ),
                timeout=8.0  # Hard limit for Vercel
            )
        except asyncio.TimeoutError:
            raise HTTPException(
                status_code=408,
                detail="Request timeout - operation exceeded time limit"
            )

        processing_time = time.time() - start_time
        logger.info(f"Scraping completed in {processing_time:.2f} seconds")

        # Sanitize content before returning
        content = ContentValidator.sanitize_content(result.get("content", ""))

        return ScrapeResponse(
            success=True,
            url=str(request.url),
            title=result.get("title", "")[:500],  # Limit title length
            content=content,
            meta_description=result.get("meta_description", "")[:500],  # Limit meta description
            links=result.get("links", [])[:50],  # Limit number of links
            images=result.get("images", [])[:20],  # Limit number of images
            processing_time=processing_time,
            timestamp=time.time()
        )

    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except asyncio.TimeoutError:
        raise HTTPException(
            status_code=408,
            detail="Request timeout - the page took too long to load"
        )
    except Exception as e:
        logger.error(f"Scraping error for {request.url}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Scraping failed: {str(e)}"
        )

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler"""
    return ErrorResponse(
        success=False,
        error=exc.detail,
        status_code=exc.status_code,
        timestamp=time.time()
    ).dict()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
